# Baolist表字段类型修改总结

## 修改概述

本次修改将Baolist表中的多个字段类型从TEXT改为更合适的数据类型，以提高数据存储效率和类型安全性。

## 字段类型修改详情

### VARCHAR字段修改
以下字段从 `db.Text()` 改为 `db.String()` (VARCHAR类型)：

| 字段名 | 原类型 | 新类型 | 长度限制 | 说明 |
|--------|--------|--------|----------|------|
| bao_name | TEXT | VARCHAR(255) | 255字符 | 项目名称 |
| bao_lx | TEXT | VARCHAR(100) | 100字符 | 项目类型 |
| bao_bm | TEXT | VARCHAR(100) | 100字符 | 项目编号 |
| bao_city | TEXT | VARCHAR(100) | 100字符 | 城市名称 |
| czry | TEXT | VARCHAR(100) | 100字符 | 操作人员 |

### DATETIME字段修改
以下字段从 `db.Text()` 改为 `db.DateTime` 类型：

| 字段名 | 原类型 | 新类型 | 允许NULL | 说明 |
|--------|--------|--------|----------|------|
| bao_start | TEXT(时间戳) | DATETIME | NO | 项目开始时间 |
| bao_end | TEXT(时间戳) | DATETIME | YES | 项目结束时间 |
| sj_ywsl | TEXT(时间戳) | DATETIME | YES | 业务受理时间 |
| sj_gdsj | TEXT(时间戳) | DATETIME | YES | 项目归档时间 |

### 删除的字段
以下时间字段已被删除（在模型和前端代码中移除）：
- sj_xckc (现场勘察时间)
- sj_fadf (方案答复时间)
- sj_jgys (竣工验收时间)
- sj_zbsd (装表送电时间)

## 代码修改详情

### 1. 模型定义修改 (app.py)

#### Baolist类字段定义
```python
# 修改前
bao_name = db.Column(db.Text(), nullable=False)
bao_start = db.Column(db.Text(), nullable=False, default=time.time())

# 修改后
bao_name = db.Column(db.String(255), nullable=False)
bao_start = db.Column(db.DateTime, nullable=False, default=db.func.now())
```

#### to_dict方法修改
```python
# 修改后 - 时间字段转换为时间戳字符串以保持前端兼容性
"bao_start": str(int(self.bao_start.timestamp())) if self.bao_start else None,
"bao_end": str(int(self.bao_end.timestamp())) if self.bao_end else None,
"sj_ywsl": str(int(self.sj_ywsl.timestamp())) if self.sj_ywsl else None,
"sj_gdsj": str(int(self.sj_gdsj.timestamp())) if self.sj_gdsj else None,
```

### 2. API接口修改

#### 创建项目接口 (/addbao)
- 移除手动设置 `bao_start` 时间戳
- 使用数据库默认值 `db.func.now()`
- 将 `bao_end` 初始化为 `None` 而不是空字符串

#### 项目完成接口
- 使用 `db.func.now()` 设置 `bao_end` 时间

#### 文件路径生成
```python
# 修改前
time.strftime("%Y%m%d%H%M%S", time.localtime(int(baolist.bao_start)))

# 修改后
baolist.bao_start.strftime("%Y%m%d%H%M%S")
```

### 3. 前端代码修改 (myjs.js)

#### 工单流程步骤定义
```javascript
// 修改后 - 只保留现有的时间字段
const stepDefinitions = [
    { name: '业务受理', timeField: 'sj_ywsl', description: '客户申请受理' },
    { name: '项目归档', timeField: 'sj_gdsj', description: '项目资料归档' }
];
```

#### 项目状态更新
```javascript
// 修改后
project.bao_end = null; // 清除完成时间
project.bao_end = Math.floor(Date.now() / 1000).toString(); // 设置完成时间
```

### 4. 批量导入脚本修改 (batch_import_projects.py)

#### 时间字段转换函数
```python
# 修改后 - 返回datetime对象而不是时间戳字符串
def convert_time_field(value):
    # ... 转换逻辑
    return dt  # 返回datetime对象
```

#### 项目创建
- 移除 `bao_start` 参数设置
- 将 `bao_end` 设置为 `None`
- 移除已删除的时间字段

## 数据库迁移

### 迁移脚本
提供了三个迁移脚本：

1. **database_migration.sql** - 完整的一次性迁移脚本
2. **migration_step_by_step.sql** - 分步骤的安全迁移脚本
3. **database_migration_helper.py** - Python辅助迁移脚本

### 迁移步骤
1. 备份原始数据
2. 添加新的临时字段
3. 数据转换和迁移
4. 验证转换结果
5. 删除原字段并重命名新字段
6. 验证最终结果

### 数据转换逻辑
- **VARCHAR字段**: 直接复制文本内容，截取到指定长度
- **DATETIME字段**: 从时间戳字符串转换为DATETIME类型
  ```sql
  CASE 
      WHEN field REGEXP '^[0-9]+$' AND CAST(field AS UNSIGNED) > 946684800
      THEN FROM_UNIXTIME(CAST(field AS UNSIGNED))
      ELSE NULL
  END
  ```

## 测试验证

### 测试脚本
1. **test_field_migration.py** - 完整的功能测试
2. **simple_model_test.py** - 简单的模型测试

### 测试内容
- 数据库连接和表结构验证
- 模型字段类型验证
- 数据序列化测试
- 时间字段处理测试
- 文件路径生成测试

## 兼容性保证

### 前端兼容性
- to_dict方法仍然返回时间戳字符串格式
- 前端时间处理代码无需修改
- API响应格式保持不变

### 数据完整性
- 所有现有数据都会被正确转换
- 时间戳转换使用安全的边界检查
- 字符串字段截取到合适长度

## 注意事项

1. **执行迁移前务必备份数据库**
2. **建议在测试环境先验证迁移脚本**
3. **迁移过程中应用需要停机**
4. **验证迁移结果后再删除备份表**

## 预期收益

1. **性能提升**: VARCHAR字段比TEXT字段查询更快
2. **存储优化**: 固定长度字段存储更高效
3. **类型安全**: DATETIME类型提供更好的时间处理
4. **索引优化**: VARCHAR字段可以更好地利用索引
5. **数据一致性**: 强类型约束减少数据错误

## 回滚方案

如果迁移后发现问题，可以：
1. 使用备份表恢复原始数据
2. 回滚代码到迁移前版本
3. 重新部署应用

备份表命名格式：`baolist_backup_YYYYMMDD_HHMMSS`
