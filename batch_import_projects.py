#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量导入项目数据脚本
从Excel文件读取高压用户项目数据并导入到数据库
"""

import pandas as pd
import time
from datetime import datetime
from app import app, db, Baolist, Sql_City
from sqlalchemy import text
import os
import sys
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def validate_required_fields(row, row_index):
    """验证必填字段"""
    required_fields = ['bao_name', 'bao_bm', 'bao_city', 'usercode', 'gds']
    missing_fields = []

    for field in required_fields:
        if pd.isna(row[field]) or str(row[field]).strip() == '':
            missing_fields.append(field)

    if missing_fields:
        return False, f"第{row_index+2}行缺少必填字段: {', '.join(missing_fields)}"

    # 验证城市是否在允许列表中
    city = str(row['bao_city']).strip()
    if city not in Sql_City:
        return False, f"第{row_index+2}行城市不在允许列表中: {city}，允许的城市: {', '.join(Sql_City)}"

    # 验证工单编号格式（应该是数字）
    bao_bm = str(row['bao_bm']).strip()
    if not bao_bm.isdigit():
        return False, f"第{row_index+2}行工单编号格式错误，应为纯数字: {bao_bm}"

    # 验证工单类型（如果提供）
    if pd.notna(row.get('gdlx')):
        gdlx = str(row['gdlx']).strip()
        valid_gdlx = ["增容", "临时用电", "新装"]
        if gdlx not in valid_gdlx:
            return False, f"第{row_index+2}行工单类型错误: {gdlx}，允许的类型: {', '.join(valid_gdlx)}"

    return True, ""

def convert_time_field(value):
    """转换时间字段，返回datetime对象"""
    if pd.isna(value) or value == '' or value is None:
        return None

    try:
        # 如果是pandas的Timestamp对象
        if isinstance(value, pd.Timestamp):
            return value.to_pydatetime()

        # 如果是字符串日期格式
        if isinstance(value, str):
            # 尝试解析常见的日期格式
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y/%m/%d %H:%M:%S', '%Y/%m/%d']:
                try:
                    dt = datetime.strptime(value, fmt)
                    return dt
                except ValueError:
                    continue

        # 如果是数字（可能是Excel的日期序列号）
        if isinstance(value, (int, float)):
            # Excel日期序列号转换（1900年1月1日为1）
            if value > 25569:  # 1970年1月1日的Excel序列号
                dt = datetime(1900, 1, 1) + pd.Timedelta(days=value-2)
                return dt.to_pydatetime() if hasattr(dt, 'to_pydatetime') else dt

        return None
    except Exception as e:
        print(f"时间字段转换失败: {value}, 错误: {e}")
        return None

def convert_numeric_field(value):
    """转换数值字段"""
    if pd.isna(value) or value == '' or value is None:
        return None
    
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def preview_data(df, num_rows=5):
    """预览数据"""
    print(f"\n📋 数据预览（前{num_rows}行）:")
    print("-" * 80)
    for i in range(min(num_rows, len(df))):
        row = df.iloc[i]
        print(f"第{i+1}行:")
        print(f"  项目名称: {row.get('bao_name', 'N/A')}")
        print(f"  工单编号: {row.get('bao_bm', 'N/A')}")
        print(f"  城市: {row.get('bao_city', 'N/A')}")
        print(f"  户号: {row.get('usercode', 'N/A')}")
        print(f"  供电所: {row.get('gds', 'N/A')}")
        print(f"  工单类型: {row.get('gdlx', 'N/A')}")
        print(f"  合同容量: {row.get('htrl', 'N/A')}")
        print()

def batch_import_projects(excel_file_path, preview_only=False):
    """批量导入项目数据"""

    # 检查文件是否存在
    if not os.path.exists(excel_file_path):
        print(f"❌ 文件不存在: {excel_file_path}")
        return False

    print(f"📁 开始读取Excel文件: {excel_file_path}")

    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file_path)
        print(f"📊 成功读取Excel文件，共 {len(df)} 行数据")

        # 显示列名
        print(f"📋 Excel列名: {list(df.columns)}")

        # 预览数据
        preview_data(df)

        if preview_only:
            return True

    except Exception as e:
        print(f"❌ 读取Excel文件失败: {str(e)}")
        return False
    
    # 统计变量
    success_count = 0
    skip_count = 0
    error_count = 0
    error_logs = []
    
    with app.app_context():
        print("🔄 开始导入数据...")
        
        for index, row in df.iterrows():
            try:
                # 验证必填字段
                is_valid, error_msg = validate_required_fields(row, index)
                if not is_valid:
                    error_logs.append(error_msg)
                    skip_count += 1
                    continue
                
                # 检查工单编号是否已存在
                existing_project = Baolist.query.filter_by(bao_bm=str(row['bao_bm']).strip()).first()
                if existing_project:
                    error_msg = f"第{index+2}行工单编号已存在: {row['bao_bm']}"
                    error_logs.append(error_msg)
                    skip_count += 1
                    continue
                
                # 转换时间字段（只保留现有的字段）
                sj_ywsl = convert_time_field(row.get('sj_ywsl'))
                sj_gdsj = convert_time_field(row.get('sj_gdsj'))
                
                # 转换合同容量
                htrl = convert_numeric_field(row.get('htrl'))
                
                # 创建新的项目记录
                new_project = Baolist(
                    bao_name=str(row['bao_name']).strip(),
                    bao_state=False,
                    bao_lx="高压用户",  # 固定为高压用户
                    bao_bm=str(row['bao_bm']).strip(),
                    bao_city=str(row['bao_city']).strip(),
                    # bao_start使用数据库默认值（当前时间）
                    bao_end=None,  # 改为None，因为现在是DATETIME类型
                    is_reject=False,
                    is_approve=False,
                    is_submit=True,
                    usercode=str(row['usercode']).strip(),
                    gds=str(row['gds']).strip(),
                    czry="批量导入",  # 操作人员标记为批量导入
                    # 新增字段
                    gdlx=str(row['gdlx']).strip() if pd.notna(row.get('gdlx')) else None,
                    sj_ywsl=sj_ywsl,
                    sj_gdsj=sj_gdsj,
                    htrl=htrl
                )
                
                # 添加到数据库会话
                db.session.add(new_project)
                success_count += 1
                
                # 每100条记录提交一次，避免内存占用过大
                if success_count % 100 == 0:
                    db.session.commit()
                    print(f"✅ 已成功导入 {success_count} 条记录...")
                
            except Exception as e:
                error_msg = f"第{index+2}行导入失败: {str(e)}"
                error_logs.append(error_msg)
                error_count += 1
                # 回滚当前事务
                db.session.rollback()
        
        # 最终提交剩余的记录
        try:
            db.session.commit()
            print("💾 数据库提交成功")
        except Exception as e:
            print(f"❌ 数据库提交失败: {str(e)}")
            db.session.rollback()
            return False
    
    # 输出统计结果
    print("\n" + "="*50)
    print("📊 导入结果统计:")
    print(f"✅ 成功导入: {success_count} 条")
    print(f"⚠️  跳过记录: {skip_count} 条")
    print(f"❌ 错误记录: {error_count} 条")
    print(f"📝 总处理: {len(df)} 条")
    
    # 输出错误日志
    if error_logs:
        print("\n" + "="*50)
        print("📋 错误详情:")
        for i, error in enumerate(error_logs[:20], 1):  # 只显示前20个错误
            print(f"{i}. {error}")
        
        if len(error_logs) > 20:
            print(f"... 还有 {len(error_logs) - 20} 个错误未显示")
    
    return success_count > 0

def main():
    """主函数"""
    excel_file_path = "高压用户.xlsx"

    print("🚀 批量导入项目数据脚本启动")
    print(f"📂 工作目录: {os.getcwd()}")
    print(f"📄 目标文件: {excel_file_path}")

    # 检查数据库连接
    try:
        with app.app_context():
            # 测试数据库连接
            db.session.execute(text('SELECT 1'))
            print("✅ 数据库连接正常")
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return

    # 首先预览数据
    print("\n🔍 正在预览数据...")
    preview_success = batch_import_projects(excel_file_path, preview_only=True)

    if not preview_success:
        print("💥 预览失败，无法继续导入")
        return

    # 询问用户是否继续
    print("\n" + "="*50)
    confirm = input("❓ 确认要导入这些数据吗？(y/N): ").strip().lower()

    if confirm not in ['y', 'yes', '是']:
        print("❌ 用户取消导入操作")
        return

    # 执行导入
    print("\n🔄 开始正式导入...")
    success = batch_import_projects(excel_file_path, preview_only=False)

    if success:
        print("\n🎉 批量导入完成！")
        print("📝 详细日志已保存到 batch_import.log 文件")
    else:
        print("\n💥 批量导入失败！")
        print("📝 错误日志已保存到 batch_import.log 文件")

def create_sample_excel():
    """创建示例Excel文件"""
    sample_data = {
        'bao_city': ['城区', '环城', '汝南'],
        'gds': ['城区专线', '城区直供', '城区供电中心'],
        'gdlx': ['新装', '增容', '临时用电'],
        'bao_bm': ['20241201001', '20241201002', '20241201003'],
        'bao_name': ['示例项目1', '示例项目2', '示例项目3'],
        'usercode': ['1001', '1002', '1003'],
        'sj_ywsl': ['2024-01-15 09:00:00', '2024-01-16 10:30:00', ''],
        'sj_xckc': ['2024-01-20 14:00:00', '', ''],
        'sj_fadf': ['2024-01-25 16:00:00', '', ''],
        'sj_jgys': ['', '', ''],
        'sj_zbsd': ['', '', ''],
        'sj_gdsj': ['', '', ''],
        'htrl': [100.5, 200.0, '']
    }

    df = pd.DataFrame(sample_data)
    sample_file = '高压用户_示例.xlsx'
    df.to_excel(sample_file, index=False)
    print(f"✅ 示例Excel文件已创建: {sample_file}")
    print("📋 请参考此文件格式准备您的数据")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == '--create-sample':
        create_sample_excel()
    else:
        main()
