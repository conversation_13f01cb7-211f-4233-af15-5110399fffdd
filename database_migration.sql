-- 数据库迁移脚本：修改Baolist表字段类型
-- 执行前请备份数据库！
-- 
-- 修改内容：
-- 1. VARCHAR字段：bao_name, bao_lx, bao_bm, bao_city, czry
-- 2. DATETIME字段：bao_start, bao_end, sj_ywsl, sj_gdsj
--
-- 执行时间：预计需要几分钟，取决于数据量

USE yekuo;

-- 开始事务
START TRANSACTION;

-- 1. 创建备份表（可选，用于安全起见）
CREATE TABLE baolist_backup AS SELECT * FROM baolist;

-- 2. 添加新的临时列用于数据转换
ALTER TABLE baolist 
ADD COLUMN bao_name_new VARCHAR(255),
ADD COLUMN bao_lx_new VARCHAR(100),
ADD COLUMN bao_bm_new VARCHAR(100),
ADD COLUMN bao_city_new VARCHAR(100),
ADD COLUMN czry_new VARCHAR(100),
ADD COLUMN bao_start_new DATETIME,
ADD COLUMN bao_end_new DATETIME,
ADD COLUMN sj_ywsl_new DATETIME,
ADD COLUMN sj_gdsj_new DATETIME;

-- 3. 数据转换和迁移

-- 转换VARCHAR字段（直接复制文本内容）
UPDATE baolist SET 
    bao_name_new = SUBSTRING(bao_name, 1, 255),
    bao_lx_new = SUBSTRING(bao_lx, 1, 100),
    bao_bm_new = SUBSTRING(bao_bm, 1, 100),
    bao_city_new = SUBSTRING(bao_city, 1, 100),
    czry_new = SUBSTRING(czry, 1, 100);

-- 转换DATETIME字段（从时间戳转换）
-- bao_start字段转换
UPDATE baolist SET 
    bao_start_new = CASE 
        WHEN bao_start REGEXP '^[0-9]+$' AND CAST(bao_start AS UNSIGNED) > 0 
        THEN FROM_UNIXTIME(CAST(bao_start AS UNSIGNED))
        ELSE NOW()  -- 如果转换失败，使用当前时间
    END;

-- bao_end字段转换（可能为空字符串）
UPDATE baolist SET 
    bao_end_new = CASE 
        WHEN bao_end = '' OR bao_end IS NULL THEN NULL
        WHEN bao_end REGEXP '^[0-9]+$' AND CAST(bao_end AS UNSIGNED) > 0 
        THEN FROM_UNIXTIME(CAST(bao_end AS UNSIGNED))
        ELSE NULL
    END;

-- sj_ywsl字段转换（可能为NULL或时间戳字符串）
UPDATE baolist SET 
    sj_ywsl_new = CASE 
        WHEN sj_ywsl IS NULL OR sj_ywsl = '' THEN NULL
        WHEN sj_ywsl REGEXP '^[0-9]+$' AND CAST(sj_ywsl AS UNSIGNED) > 0 
        THEN FROM_UNIXTIME(CAST(sj_ywsl AS UNSIGNED))
        ELSE NULL
    END;

-- sj_gdsj字段转换（可能为NULL或时间戳字符串）
UPDATE baolist SET 
    sj_gdsj_new = CASE 
        WHEN sj_gdsj IS NULL OR sj_gdsj = '' THEN NULL
        WHEN sj_gdsj REGEXP '^[0-9]+$' AND CAST(sj_gdsj AS UNSIGNED) > 0 
        THEN FROM_UNIXTIME(CAST(sj_gdsj AS UNSIGNED))
        ELSE NULL
    END;

-- 4. 验证数据转换结果
-- 检查是否有转换失败的记录
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN bao_name_new IS NULL THEN 1 END) as null_bao_name,
    COUNT(CASE WHEN bao_lx_new IS NULL THEN 1 END) as null_bao_lx,
    COUNT(CASE WHEN bao_bm_new IS NULL THEN 1 END) as null_bao_bm,
    COUNT(CASE WHEN bao_city_new IS NULL THEN 1 END) as null_bao_city,
    COUNT(CASE WHEN czry_new IS NULL THEN 1 END) as null_czry,
    COUNT(CASE WHEN bao_start_new IS NULL THEN 1 END) as null_bao_start
FROM baolist;

-- 如果上述查询结果显示有NULL值在不应该为NULL的字段中，请检查数据并修复后再继续

-- 5. 删除原字段并重命名新字段
ALTER TABLE baolist 
DROP COLUMN bao_name,
DROP COLUMN bao_lx,
DROP COLUMN bao_bm,
DROP COLUMN bao_city,
DROP COLUMN czry,
DROP COLUMN bao_start,
DROP COLUMN bao_end,
DROP COLUMN sj_ywsl,
DROP COLUMN sj_gdsj;

-- 重命名新字段
ALTER TABLE baolist 
CHANGE COLUMN bao_name_new bao_name VARCHAR(255) NOT NULL,
CHANGE COLUMN bao_lx_new bao_lx VARCHAR(100) NOT NULL,
CHANGE COLUMN bao_bm_new bao_bm VARCHAR(100) NOT NULL,
CHANGE COLUMN bao_city_new bao_city VARCHAR(100) NOT NULL,
CHANGE COLUMN czry_new czry VARCHAR(100) NOT NULL,
CHANGE COLUMN bao_start_new bao_start DATETIME NOT NULL,
CHANGE COLUMN bao_end_new bao_end DATETIME NULL,
CHANGE COLUMN sj_ywsl_new sj_ywsl DATETIME NULL,
CHANGE COLUMN sj_gdsj_new sj_gdsj DATETIME NULL;

-- 6. 重新创建索引（如果需要）
-- 检查原有索引
SHOW INDEX FROM baolist;

-- 根据需要重新创建索引，例如：
-- CREATE INDEX idx_bao_bm ON baolist(bao_bm);
-- CREATE INDEX idx_bao_city ON baolist(bao_city);

-- 7. 验证最终结果
DESCRIBE baolist;

-- 检查数据完整性
SELECT COUNT(*) as total_records FROM baolist;
SELECT COUNT(*) as records_with_valid_start FROM baolist WHERE bao_start IS NOT NULL;

-- 提交事务
COMMIT;

-- 如果一切正常，可以删除备份表（可选）
-- DROP TABLE baolist_backup;

-- 迁移完成提示
SELECT 'Database migration completed successfully!' as status;
