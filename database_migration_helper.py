#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移辅助脚本
用于安全地执行Baolist表字段类型修改

使用方法：
1. 确保数据库连接配置正确
2. 运行脚本进行数据验证和迁移
3. 按提示确认每个步骤
"""

import pymysql
import time
import datetime
import sys
import os

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'Aa1472356890',
    'database': 'yekuo',
    'charset': 'utf8mb4'
}

class DatabaseMigrator:
    def __init__(self):
        self.connection = None
        self.backup_table_name = f"baolist_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**DB_CONFIG)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def execute_query(self, query, fetch=False):
        """执行SQL查询"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query)
                if fetch:
                    return cursor.fetchall()
                self.connection.commit()
                return True
        except Exception as e:
            print(f"❌ SQL执行失败: {e}")
            print(f"SQL: {query}")
            self.connection.rollback()
            return False
    
    def check_table_exists(self):
        """检查表是否存在"""
        query = "SHOW TABLES LIKE 'baolist'"
        result = self.execute_query(query, fetch=True)
        return len(result) > 0
    
    def get_table_info(self):
        """获取表结构信息"""
        query = "DESCRIBE baolist"
        return self.execute_query(query, fetch=True)
    
    def get_record_count(self):
        """获取记录总数"""
        query = "SELECT COUNT(*) FROM baolist"
        result = self.execute_query(query, fetch=True)
        return result[0][0] if result else 0
    
    def create_backup(self):
        """创建备份表"""
        print(f"📋 创建备份表: {self.backup_table_name}")
        query = f"CREATE TABLE {self.backup_table_name} AS SELECT * FROM baolist"
        if self.execute_query(query):
            print("✅ 备份表创建成功")
            return True
        return False
    
    def analyze_data_format(self):
        """分析当前数据格式"""
        print("🔍 分析当前数据格式...")
        
        # 检查字符串字段长度
        length_query = """
        SELECT 
            MAX(LENGTH(bao_name)) as max_bao_name_length,
            MAX(LENGTH(bao_lx)) as max_bao_lx_length,
            MAX(LENGTH(bao_bm)) as max_bao_bm_length,
            MAX(LENGTH(bao_city)) as max_bao_city_length,
            MAX(LENGTH(czry)) as max_czry_length
        FROM baolist
        """
        length_result = self.execute_query(length_query, fetch=True)
        if length_result:
            lengths = length_result[0]
            print(f"字段长度分析:")
            print(f"  bao_name最大长度: {lengths[0]}")
            print(f"  bao_lx最大长度: {lengths[1]}")
            print(f"  bao_bm最大长度: {lengths[2]}")
            print(f"  bao_city最大长度: {lengths[3]}")
            print(f"  czry最大长度: {lengths[4]}")
        
        # 检查时间戳格式
        timestamp_query = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN bao_start REGEXP '^[0-9]+$' THEN 1 END) as valid_bao_start,
            COUNT(CASE WHEN bao_end = '' THEN 1 END) as empty_bao_end,
            COUNT(CASE WHEN bao_end REGEXP '^[0-9]+$' THEN 1 END) as valid_bao_end,
            COUNT(CASE WHEN sj_ywsl IS NOT NULL AND sj_ywsl != '' THEN 1 END) as non_empty_sj_ywsl,
            COUNT(CASE WHEN sj_gdsj IS NOT NULL AND sj_gdsj != '' THEN 1 END) as non_empty_sj_gdsj
        FROM baolist
        """
        timestamp_result = self.execute_query(timestamp_query, fetch=True)
        if timestamp_result:
            stats = timestamp_result[0]
            print(f"时间戳格式分析:")
            print(f"  总记录数: {stats[0]}")
            print(f"  有效bao_start: {stats[1]}")
            print(f"  空bao_end: {stats[2]}")
            print(f"  有效bao_end: {stats[3]}")
            print(f"  非空sj_ywsl: {stats[4]}")
            print(f"  非空sj_gdsj: {stats[5]}")
    
    def add_new_columns(self):
        """添加新字段"""
        print("➕ 添加新字段...")
        queries = [
            "ALTER TABLE baolist ADD COLUMN bao_name_new VARCHAR(255) NULL",
            "ALTER TABLE baolist ADD COLUMN bao_lx_new VARCHAR(100) NULL",
            "ALTER TABLE baolist ADD COLUMN bao_bm_new VARCHAR(100) NULL",
            "ALTER TABLE baolist ADD COLUMN bao_city_new VARCHAR(100) NULL",
            "ALTER TABLE baolist ADD COLUMN czry_new VARCHAR(100) NULL",
            "ALTER TABLE baolist ADD COLUMN bao_start_new DATETIME NULL",
            "ALTER TABLE baolist ADD COLUMN bao_end_new DATETIME NULL",
            "ALTER TABLE baolist ADD COLUMN sj_ywsl_new DATETIME NULL",
            "ALTER TABLE baolist ADD COLUMN sj_gdsj_new DATETIME NULL"
        ]
        
        for query in queries:
            if not self.execute_query(query):
                return False
        
        print("✅ 新字段添加成功")
        return True
    
    def convert_varchar_fields(self):
        """转换VARCHAR字段"""
        print("🔄 转换VARCHAR字段...")
        queries = [
            "UPDATE baolist SET bao_name_new = TRIM(SUBSTRING(bao_name, 1, 255))",
            "UPDATE baolist SET bao_lx_new = TRIM(SUBSTRING(bao_lx, 1, 100))",
            "UPDATE baolist SET bao_bm_new = TRIM(SUBSTRING(bao_bm, 1, 100))",
            "UPDATE baolist SET bao_city_new = TRIM(SUBSTRING(bao_city, 1, 100))",
            "UPDATE baolist SET czry_new = TRIM(SUBSTRING(czry, 1, 100))"
        ]
        
        for query in queries:
            if not self.execute_query(query):
                return False
        
        print("✅ VARCHAR字段转换完成")
        return True
    
    def convert_datetime_fields(self):
        """转换DATETIME字段"""
        print("🔄 转换DATETIME字段...")
        
        # 转换bao_start
        query1 = """
        UPDATE baolist SET 
            bao_start_new = CASE 
                WHEN bao_start REGEXP '^[0-9]+$' AND CAST(bao_start AS UNSIGNED) > 946684800
                THEN FROM_UNIXTIME(CAST(bao_start AS UNSIGNED))
                ELSE NOW()
            END
        """
        
        # 转换bao_end
        query2 = """
        UPDATE baolist SET 
            bao_end_new = CASE 
                WHEN bao_end = '' OR bao_end IS NULL THEN NULL
                WHEN bao_end REGEXP '^[0-9]+$' AND CAST(bao_end AS UNSIGNED) > 946684800
                THEN FROM_UNIXTIME(CAST(bao_end AS UNSIGNED))
                ELSE NULL
            END
        """
        
        # 转换sj_ywsl
        query3 = """
        UPDATE baolist SET 
            sj_ywsl_new = CASE 
                WHEN sj_ywsl IS NULL OR sj_ywsl = '' THEN NULL
                WHEN sj_ywsl REGEXP '^[0-9]+$' AND CAST(sj_ywsl AS UNSIGNED) > 946684800
                THEN FROM_UNIXTIME(CAST(sj_ywsl AS UNSIGNED))
                ELSE NULL
            END
        """
        
        # 转换sj_gdsj
        query4 = """
        UPDATE baolist SET 
            sj_gdsj_new = CASE 
                WHEN sj_gdsj IS NULL OR sj_gdsj = '' THEN NULL
                WHEN sj_gdsj REGEXP '^[0-9]+$' AND CAST(sj_gdsj AS UNSIGNED) > 946684800
                THEN FROM_UNIXTIME(CAST(sj_gdsj AS UNSIGNED))
                ELSE NULL
            END
        """
        
        queries = [query1, query2, query3, query4]
        for i, query in enumerate(queries, 1):
            print(f"  转换字段 {i}/4...")
            if not self.execute_query(query):
                return False
        
        print("✅ DATETIME字段转换完成")
        return True
    
    def validate_conversion(self):
        """验证转换结果"""
        print("✅ 验证转换结果...")
        
        validation_query = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN bao_name_new IS NULL OR bao_name_new = '' THEN 1 END) as empty_bao_name,
            COUNT(CASE WHEN bao_lx_new IS NULL OR bao_lx_new = '' THEN 1 END) as empty_bao_lx,
            COUNT(CASE WHEN bao_bm_new IS NULL OR bao_bm_new = '' THEN 1 END) as empty_bao_bm,
            COUNT(CASE WHEN bao_city_new IS NULL OR bao_city_new = '' THEN 1 END) as empty_bao_city,
            COUNT(CASE WHEN czry_new IS NULL OR czry_new = '' THEN 1 END) as empty_czry,
            COUNT(CASE WHEN bao_start_new IS NULL THEN 1 END) as null_bao_start
        FROM baolist
        """
        
        result = self.execute_query(validation_query, fetch=True)
        if result:
            stats = result[0]
            print(f"验证结果:")
            print(f"  总记录数: {stats[0]}")
            print(f"  空bao_name: {stats[1]}")
            print(f"  空bao_lx: {stats[2]}")
            print(f"  空bao_bm: {stats[3]}")
            print(f"  空bao_city: {stats[4]}")
            print(f"  空czry: {stats[5]}")
            print(f"  空bao_start: {stats[6]}")
            
            # 检查是否有问题
            if any(stats[1:6]) or stats[6] > 0:
                print("⚠️  发现数据问题，请检查后再继续")
                return False
            else:
                print("✅ 数据验证通过")
                return True
        return False
    
    def finalize_migration(self):
        """完成迁移（替换字段）"""
        print("🔄 执行最终迁移...")
        
        try:
            # 开始事务
            self.connection.begin()
            
            # 删除原字段
            drop_queries = [
                "ALTER TABLE baolist DROP COLUMN bao_name",
                "ALTER TABLE baolist DROP COLUMN bao_lx",
                "ALTER TABLE baolist DROP COLUMN bao_bm",
                "ALTER TABLE baolist DROP COLUMN bao_city",
                "ALTER TABLE baolist DROP COLUMN czry",
                "ALTER TABLE baolist DROP COLUMN bao_start",
                "ALTER TABLE baolist DROP COLUMN bao_end",
                "ALTER TABLE baolist DROP COLUMN sj_ywsl",
                "ALTER TABLE baolist DROP COLUMN sj_gdsj"
            ]
            
            for query in drop_queries:
                if not self.execute_query(query):
                    raise Exception("删除原字段失败")
            
            # 重命名新字段
            rename_queries = [
                "ALTER TABLE baolist CHANGE COLUMN bao_name_new bao_name VARCHAR(255) NOT NULL",
                "ALTER TABLE baolist CHANGE COLUMN bao_lx_new bao_lx VARCHAR(100) NOT NULL",
                "ALTER TABLE baolist CHANGE COLUMN bao_bm_new bao_bm VARCHAR(100) NOT NULL",
                "ALTER TABLE baolist CHANGE COLUMN bao_city_new bao_city VARCHAR(100) NOT NULL",
                "ALTER TABLE baolist CHANGE COLUMN czry_new czry VARCHAR(100) NOT NULL",
                "ALTER TABLE baolist CHANGE COLUMN bao_start_new bao_start DATETIME NOT NULL",
                "ALTER TABLE baolist CHANGE COLUMN bao_end_new bao_end DATETIME NULL",
                "ALTER TABLE baolist CHANGE COLUMN sj_ywsl_new sj_ywsl DATETIME NULL",
                "ALTER TABLE baolist CHANGE COLUMN sj_gdsj_new sj_gdsj DATETIME NULL"
            ]
            
            for query in rename_queries:
                if not self.execute_query(query):
                    raise Exception("重命名字段失败")
            
            # 提交事务
            self.connection.commit()
            print("✅ 迁移完成")
            return True
            
        except Exception as e:
            print(f"❌ 迁移失败: {e}")
            self.connection.rollback()
            return False
    
    def run_migration(self):
        """运行完整迁移流程"""
        print("🚀 开始数据库迁移...")
        print("=" * 50)
        
        # 连接数据库
        if not self.connect():
            return False
        
        # 检查表
        if not self.check_table_exists():
            print("❌ baolist表不存在")
            return False
        
        # 获取记录数
        record_count = self.get_record_count()
        print(f"📊 当前记录数: {record_count}")
        
        # 分析数据
        self.analyze_data_format()
        
        # 确认继续
        if input("\n是否继续迁移？(y/N): ").lower() != 'y':
            print("❌ 用户取消迁移")
            return False
        
        # 创建备份
        if not self.create_backup():
            return False
        
        # 添加新字段
        if not self.add_new_columns():
            return False
        
        # 转换VARCHAR字段
        if not self.convert_varchar_fields():
            return False
        
        # 转换DATETIME字段
        if not self.convert_datetime_fields():
            return False
        
        # 验证转换
        if not self.validate_conversion():
            return False
        
        # 最终确认
        if input("\n验证通过，是否执行最终迁移？(y/N): ").lower() != 'y':
            print("❌ 用户取消最终迁移")
            return False
        
        # 完成迁移
        if not self.finalize_migration():
            return False
        
        print("\n🎉 数据库迁移成功完成！")
        print(f"📋 备份表名: {self.backup_table_name}")
        print("💡 如确认迁移无误，可删除备份表")
        
        return True
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()

if __name__ == "__main__":
    migrator = DatabaseMigrator()
    try:
        success = migrator.run_migration()
        sys.exit(0 if success else 1)
    finally:
        migrator.close()
