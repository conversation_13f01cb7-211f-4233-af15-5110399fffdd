-- 分步数据库迁移脚本
-- 请按顺序执行每个步骤，并在每步后验证结果

-- ============================================
-- 步骤1：备份数据
-- ============================================
USE yekuo;

-- 创建完整备份表
CREATE TABLE baolist_backup_$(date +%Y%m%d) AS SELECT * FROM baolist;

-- 验证备份
SELECT COUNT(*) as original_count FROM baolist;
SELECT COUNT(*) as backup_count FROM baolist_backup_$(date +%Y%m%d);

-- ============================================
-- 步骤2：检查当前数据格式
-- ============================================

-- 检查时间戳字段的数据格式
SELECT 
    id,
    bao_start,
    bao_end,
    sj_ywsl,
    sj_gdsj,
    CASE 
        WHEN bao_start REGEXP '^[0-9]+$' THEN 'valid_timestamp'
        ELSE 'invalid_format'
    END as bao_start_format,
    CASE 
        WHEN bao_end = '' THEN 'empty'
        WHEN bao_end REGEXP '^[0-9]+$' THEN 'valid_timestamp'
        ELSE 'invalid_format'
    END as bao_end_format
FROM baolist 
LIMIT 10;

-- 检查字符串字段长度
SELECT 
    MAX(LENGTH(bao_name)) as max_bao_name_length,
    MAX(LENGTH(bao_lx)) as max_bao_lx_length,
    MAX(LENGTH(bao_bm)) as max_bao_bm_length,
    MAX(LENGTH(bao_city)) as max_bao_city_length,
    MAX(LENGTH(czry)) as max_czry_length
FROM baolist;

-- ============================================
-- 步骤3：添加新字段
-- ============================================

ALTER TABLE baolist 
ADD COLUMN bao_name_new VARCHAR(255) NULL,
ADD COLUMN bao_lx_new VARCHAR(100) NULL,
ADD COLUMN bao_bm_new VARCHAR(100) NULL,
ADD COLUMN bao_city_new VARCHAR(100) NULL,
ADD COLUMN czry_new VARCHAR(100) NULL;

-- 验证字段添加
DESCRIBE baolist;

-- ============================================
-- 步骤4：转换VARCHAR字段
-- ============================================

-- 转换bao_name
UPDATE baolist SET bao_name_new = TRIM(SUBSTRING(bao_name, 1, 255));

-- 转换bao_lx  
UPDATE baolist SET bao_lx_new = TRIM(SUBSTRING(bao_lx, 1, 100));

-- 转换bao_bm
UPDATE baolist SET bao_bm_new = TRIM(SUBSTRING(bao_bm, 1, 100));

-- 转换bao_city
UPDATE baolist SET bao_city_new = TRIM(SUBSTRING(bao_city, 1, 100));

-- 转换czry
UPDATE baolist SET czry_new = TRIM(SUBSTRING(czry, 1, 100));

-- 验证VARCHAR字段转换
SELECT 
    COUNT(*) as total,
    COUNT(CASE WHEN bao_name_new IS NULL OR bao_name_new = '' THEN 1 END) as empty_bao_name,
    COUNT(CASE WHEN bao_lx_new IS NULL OR bao_lx_new = '' THEN 1 END) as empty_bao_lx,
    COUNT(CASE WHEN bao_bm_new IS NULL OR bao_bm_new = '' THEN 1 END) as empty_bao_bm,
    COUNT(CASE WHEN bao_city_new IS NULL OR bao_city_new = '' THEN 1 END) as empty_bao_city,
    COUNT(CASE WHEN czry_new IS NULL OR czry_new = '' THEN 1 END) as empty_czry
FROM baolist;

-- ============================================
-- 步骤5：添加DATETIME字段
-- ============================================

ALTER TABLE baolist 
ADD COLUMN bao_start_new DATETIME NULL,
ADD COLUMN bao_end_new DATETIME NULL,
ADD COLUMN sj_ywsl_new DATETIME NULL,
ADD COLUMN sj_gdsj_new DATETIME NULL;

-- ============================================
-- 步骤6：转换DATETIME字段
-- ============================================

-- 转换bao_start（必须字段）
UPDATE baolist SET 
    bao_start_new = CASE 
        WHEN bao_start REGEXP '^[0-9]+$' AND CAST(bao_start AS UNSIGNED) > 946684800 -- 2000-01-01之后
        THEN FROM_UNIXTIME(CAST(bao_start AS UNSIGNED))
        ELSE NOW()  -- 默认使用当前时间
    END;

-- 转换bao_end（可选字段）
UPDATE baolist SET 
    bao_end_new = CASE 
        WHEN bao_end = '' OR bao_end IS NULL THEN NULL
        WHEN bao_end REGEXP '^[0-9]+$' AND CAST(bao_end AS UNSIGNED) > 946684800
        THEN FROM_UNIXTIME(CAST(bao_end AS UNSIGNED))
        ELSE NULL
    END;

-- 转换sj_ywsl（可选字段）
UPDATE baolist SET 
    sj_ywsl_new = CASE 
        WHEN sj_ywsl IS NULL OR sj_ywsl = '' THEN NULL
        WHEN sj_ywsl REGEXP '^[0-9]+$' AND CAST(sj_ywsl AS UNSIGNED) > 946684800
        THEN FROM_UNIXTIME(CAST(sj_ywsl AS UNSIGNED))
        ELSE NULL
    END;

-- 转换sj_gdsj（可选字段）
UPDATE baolist SET 
    sj_gdsj_new = CASE 
        WHEN sj_gdsj IS NULL OR sj_gdsj = '' THEN NULL
        WHEN sj_gdsj REGEXP '^[0-9]+$' AND CAST(sj_gdsj AS UNSIGNED) > 946684800
        THEN FROM_UNIXTIME(CAST(sj_gdsj AS UNSIGNED))
        ELSE NULL
    END;

-- 验证DATETIME字段转换
SELECT 
    COUNT(*) as total,
    COUNT(CASE WHEN bao_start_new IS NULL THEN 1 END) as null_bao_start,
    COUNT(CASE WHEN bao_end_new IS NOT NULL THEN 1 END) as non_null_bao_end,
    COUNT(CASE WHEN sj_ywsl_new IS NOT NULL THEN 1 END) as non_null_sj_ywsl,
    COUNT(CASE WHEN sj_gdsj_new IS NOT NULL THEN 1 END) as non_null_sj_gdsj,
    MIN(bao_start_new) as min_start_date,
    MAX(bao_start_new) as max_start_date
FROM baolist;

-- 显示转换示例
SELECT 
    id,
    bao_start as old_start,
    bao_start_new as new_start,
    bao_end as old_end,
    bao_end_new as new_end
FROM baolist 
LIMIT 5;

-- ============================================
-- 步骤7：最终替换（请确认上述验证无误后执行）
-- ============================================

-- 注意：执行此步骤前请确保所有验证都通过！

-- 开始事务
START TRANSACTION;

-- 删除原字段
ALTER TABLE baolist 
DROP COLUMN bao_name,
DROP COLUMN bao_lx,
DROP COLUMN bao_bm,
DROP COLUMN bao_city,
DROP COLUMN czry,
DROP COLUMN bao_start,
DROP COLUMN bao_end,
DROP COLUMN sj_ywsl,
DROP COLUMN sj_gdsj;

-- 重命名新字段并设置约束
ALTER TABLE baolist 
CHANGE COLUMN bao_name_new bao_name VARCHAR(255) NOT NULL,
CHANGE COLUMN bao_lx_new bao_lx VARCHAR(100) NOT NULL,
CHANGE COLUMN bao_bm_new bao_bm VARCHAR(100) NOT NULL,
CHANGE COLUMN bao_city_new bao_city VARCHAR(100) NOT NULL,
CHANGE COLUMN czry_new czry VARCHAR(100) NOT NULL,
CHANGE COLUMN bao_start_new bao_start DATETIME NOT NULL,
CHANGE COLUMN bao_end_new bao_end DATETIME NULL,
CHANGE COLUMN sj_ywsl_new sj_ywsl DATETIME NULL,
CHANGE COLUMN sj_gdsj_new sj_gdsj DATETIME NULL;

-- 提交事务
COMMIT;

-- 最终验证
DESCRIBE baolist;
SELECT COUNT(*) as final_count FROM baolist;

SELECT 'Migration completed successfully!' as status;
