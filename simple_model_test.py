#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的模型测试脚本
用于验证Baolist模型的字段定义是否正确
"""

import sys
import os
import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_import():
    """测试模型导入"""
    try:
        from app import Baolist, db
        print("✅ 模型导入成功")
        return True, Baolist, db
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False, None, None

def test_model_creation():
    """测试模型实例创建"""
    try:
        success, Baolist, db = test_model_import()
        if not success:
            return False
        
        # 创建测试实例
        test_project = Baolist(
            bao_name="测试项目名称",
            bao_state=False,
            bao_lx="高压用户",
            bao_bm="20241201001",
            bao_city="测试城市",
            usercode="12345678",
            gds="测试供电所",
            czry="测试操作员",
            gdlx="新装",
            htrl=100.5,
            sj_ywsl=datetime.datetime.now(),
            sj_gdsj=None
        )
        
        print("✅ 模型实例创建成功")
        
        # 测试字段访问
        print(f"  项目名称: {test_project.bao_name}")
        print(f"  项目类型: {test_project.bao_lx}")
        print(f"  项目编号: {test_project.bao_bm}")
        print(f"  城市: {test_project.bao_city}")
        print(f"  操作员: {test_project.czry}")
        print(f"  开始时间: {test_project.bao_start}")
        print(f"  结束时间: {test_project.bao_end}")
        print(f"  业务受理时间: {test_project.sj_ywsl}")
        print(f"  项目归档时间: {test_project.sj_gdsj}")
        
        return True, test_project
        
    except Exception as e:
        print(f"❌ 模型实例创建失败: {e}")
        return False, None

def test_to_dict():
    """测试to_dict方法"""
    try:
        success, test_project = test_model_creation()
        if not success:
            return False
        
        # 测试序列化
        data_dict = test_project.to_dict()
        print("✅ to_dict方法执行成功")
        
        # 检查关键字段
        required_fields = ['bao_name', 'bao_lx', 'bao_bm', 'bao_city', 'czry', 
                          'bao_start', 'bao_end', 'sj_ywsl', 'sj_gdsj']
        
        for field in required_fields:
            if field in data_dict:
                print(f"  {field}: {data_dict[field]} ({type(data_dict[field]).__name__})")
            else:
                print(f"  ❌ 缺少字段: {field}")
                return False
        
        # 验证时间字段格式
        time_fields = ['bao_start', 'bao_end', 'sj_ywsl', 'sj_gdsj']
        for field in time_fields:
            value = data_dict[field]
            if value is not None:
                if not isinstance(value, str):
                    print(f"  ❌ 时间字段{field}不是字符串格式: {type(value)}")
                    return False
                try:
                    # 尝试转换为整数（时间戳）
                    int(value)
                    print(f"  ✅ 时间字段{field}格式正确")
                except ValueError:
                    print(f"  ❌ 时间字段{field}不是有效的时间戳: {value}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ to_dict方法测试失败: {e}")
        return False

def test_field_types():
    """测试字段类型"""
    try:
        from app import Baolist
        from sqlalchemy import String, DateTime, Boolean, Integer, Float, Text
        
        print("🔍 检查字段类型定义...")
        
        # 检查VARCHAR字段
        varchar_fields = {
            'bao_name': String,
            'bao_lx': String,
            'bao_bm': String,
            'bao_city': String,
            'czry': String
        }
        
        for field_name, expected_type in varchar_fields.items():
            if hasattr(Baolist, field_name):
                field = getattr(Baolist.__table__.columns, field_name)
                if isinstance(field.type, expected_type):
                    print(f"  ✅ {field_name}: {field.type}")
                else:
                    print(f"  ❌ {field_name}: 期望{expected_type.__name__}，实际{type(field.type).__name__}")
                    return False
            else:
                print(f"  ❌ 字段{field_name}不存在")
                return False
        
        # 检查DATETIME字段
        datetime_fields = ['bao_start', 'bao_end', 'sj_ywsl', 'sj_gdsj']
        for field_name in datetime_fields:
            if hasattr(Baolist, field_name):
                field = getattr(Baolist.__table__.columns, field_name)
                if isinstance(field.type, DateTime):
                    print(f"  ✅ {field_name}: {field.type}")
                else:
                    print(f"  ❌ {field_name}: 期望DateTime，实际{type(field.type).__name__}")
                    return False
            else:
                print(f"  ❌ 字段{field_name}不存在")
                return False
        
        print("✅ 所有字段类型检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 字段类型检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单模型测试...")
    print("=" * 50)
    
    tests = [
        ("模型导入", lambda: test_model_import()[0]),
        ("模型实例创建", lambda: test_model_creation()[0]),
        ("字段类型检查", test_field_types),
        ("to_dict方法", test_to_dict)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模型定义正确！")
        return True
    else:
        print("⚠️  部分测试失败，请检查模型定义")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
