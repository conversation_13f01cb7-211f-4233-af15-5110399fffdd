#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段类型迁移测试脚本
用于验证Baolist表字段类型修改后的功能是否正常

测试内容：
1. 数据库连接和表结构验证
2. 模型字段类型验证
3. API接口功能测试
4. 数据序列化测试
5. 时间字段处理测试
"""

import sys
import os
import time
import datetime
import json
import requests
from datetime import datetime as dt

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入Flask应用和模型
from app import app, db, Baolist, User

class FieldMigrationTester:
    def __init__(self):
        self.app = app
        self.test_results = []
        self.base_url = "http://localhost:5000"
        self.test_token = None
    
    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
    
    def test_database_connection(self):
        """测试数据库连接"""
        try:
            with self.app.app_context():
                # 测试数据库连接
                db.engine.execute("SELECT 1")
                self.log_test("数据库连接", True)
                return True
        except Exception as e:
            self.log_test("数据库连接", False, str(e))
            return False
    
    def test_table_structure(self):
        """测试表结构"""
        try:
            with self.app.app_context():
                # 检查表是否存在
                result = db.engine.execute("SHOW TABLES LIKE 'baolist'")
                if not result.fetchone():
                    self.log_test("表结构检查", False, "baolist表不存在")
                    return False
                
                # 检查字段类型
                result = db.engine.execute("DESCRIBE baolist")
                columns = {row[0]: row[1] for row in result.fetchall()}
                
                # 验证VARCHAR字段
                varchar_fields = {
                    'bao_name': 'varchar(255)',
                    'bao_lx': 'varchar(100)',
                    'bao_bm': 'varchar(100)',
                    'bao_city': 'varchar(100)',
                    'czry': 'varchar(100)'
                }
                
                for field, expected_type in varchar_fields.items():
                    if field not in columns:
                        self.log_test("表结构检查", False, f"字段{field}不存在")
                        return False
                    if not columns[field].startswith('varchar'):
                        self.log_test("表结构检查", False, f"字段{field}类型不正确: {columns[field]}")
                        return False
                
                # 验证DATETIME字段
                datetime_fields = ['bao_start', 'bao_end', 'sj_ywsl', 'sj_gdsj']
                for field in datetime_fields:
                    if field not in columns:
                        self.log_test("表结构检查", False, f"字段{field}不存在")
                        return False
                    if columns[field] != 'datetime':
                        self.log_test("表结构检查", False, f"字段{field}类型不正确: {columns[field]}")
                        return False
                
                self.log_test("表结构检查", True)
                return True
                
        except Exception as e:
            self.log_test("表结构检查", False, str(e))
            return False
    
    def test_model_fields(self):
        """测试模型字段定义"""
        try:
            with self.app.app_context():
                # 检查模型字段类型
                from sqlalchemy import String, DateTime
                
                # 检查VARCHAR字段
                varchar_fields = ['bao_name', 'bao_lx', 'bao_bm', 'bao_city', 'czry']
                for field_name in varchar_fields:
                    if not hasattr(Baolist, field_name):
                        self.log_test("模型字段检查", False, f"模型缺少字段{field_name}")
                        return False
                    
                    field = getattr(Baolist.__table__.columns, field_name)
                    if not isinstance(field.type, String):
                        self.log_test("模型字段检查", False, f"字段{field_name}类型不是String")
                        return False
                
                # 检查DATETIME字段
                datetime_fields = ['bao_start', 'bao_end', 'sj_ywsl', 'sj_gdsj']
                for field_name in datetime_fields:
                    if not hasattr(Baolist, field_name):
                        self.log_test("模型字段检查", False, f"模型缺少字段{field_name}")
                        return False
                    
                    field = getattr(Baolist.__table__.columns, field_name)
                    if not isinstance(field.type, DateTime):
                        self.log_test("模型字段检查", False, f"字段{field_name}类型不是DateTime")
                        return False
                
                self.log_test("模型字段检查", True)
                return True
                
        except Exception as e:
            self.log_test("模型字段检查", False, str(e))
            return False
    
    def test_data_serialization(self):
        """测试数据序列化"""
        try:
            with self.app.app_context():
                # 创建测试数据
                test_project = Baolist(
                    bao_name="测试项目",
                    bao_state=False,
                    bao_lx="高压用户",
                    bao_bm="20241201001",
                    bao_city="测试城市",
                    usercode="12345678",
                    gds="测试供电所",
                    czry="测试用户",
                    gdlx="新装",
                    htrl=100.5
                )
                
                # 测试to_dict方法
                data_dict = test_project.to_dict()
                
                # 验证字段存在
                required_fields = ['bao_name', 'bao_lx', 'bao_bm', 'bao_city', 'czry', 
                                 'bao_start', 'bao_end', 'sj_ywsl', 'sj_gdsj']
                for field in required_fields:
                    if field not in data_dict:
                        self.log_test("数据序列化", False, f"序列化结果缺少字段{field}")
                        return False
                
                # 验证时间字段格式（应该是字符串或None）
                time_fields = ['bao_start', 'bao_end', 'sj_ywsl', 'sj_gdsj']
                for field in time_fields:
                    value = data_dict[field]
                    if value is not None and not isinstance(value, str):
                        self.log_test("数据序列化", False, f"时间字段{field}格式不正确: {type(value)}")
                        return False
                
                # 验证JSON序列化
                json_str = json.dumps(data_dict)
                json.loads(json_str)  # 验证可以反序列化
                
                self.log_test("数据序列化", True)
                return True
                
        except Exception as e:
            self.log_test("数据序列化", False, str(e))
            return False
    
    def test_datetime_handling(self):
        """测试时间字段处理"""
        try:
            with self.app.app_context():
                # 测试创建带时间的记录
                now = datetime.datetime.now()
                
                test_project = Baolist(
                    bao_name="时间测试项目",
                    bao_state=False,
                    bao_lx="高压用户",
                    bao_bm="20241201002",
                    bao_city="测试城市",
                    usercode="12345679",
                    gds="测试供电所",
                    czry="测试用户",
                    sj_ywsl=now,
                    sj_gdsj=now
                )
                
                # 验证时间字段设置
                if not isinstance(test_project.sj_ywsl, datetime.datetime):
                    self.log_test("时间字段处理", False, "sj_ywsl不是datetime类型")
                    return False
                
                if not isinstance(test_project.sj_gdsj, datetime.datetime):
                    self.log_test("时间字段处理", False, "sj_gdsj不是datetime类型")
                    return False
                
                # 测试序列化
                data_dict = test_project.to_dict()
                if not isinstance(data_dict['sj_ywsl'], str):
                    self.log_test("时间字段处理", False, "序列化后sj_ywsl不是字符串")
                    return False
                
                # 验证时间戳转换
                timestamp = int(data_dict['sj_ywsl'])
                converted_time = datetime.datetime.fromtimestamp(timestamp)
                if abs((converted_time - now).total_seconds()) > 2:  # 允许2秒误差
                    self.log_test("时间字段处理", False, "时间戳转换不准确")
                    return False
                
                self.log_test("时间字段处理", True)
                return True
                
        except Exception as e:
            self.log_test("时间字段处理", False, str(e))
            return False
    
    def test_file_path_generation(self):
        """测试文件路径生成"""
        try:
            with self.app.app_context():
                # 创建测试项目
                test_project = Baolist(
                    bao_name="路径测试项目",
                    bao_state=False,
                    bao_lx="高压用户",
                    bao_bm="20241201003",
                    bao_city="测试城市",
                    usercode="12345680",
                    gds="测试供电所",
                    czry="测试用户"
                )
                
                # 模拟文件路径生成逻辑
                try:
                    path_component = test_project.bao_start.strftime("%Y%m%d%H%M%S")
                    if not path_component:
                        self.log_test("文件路径生成", False, "无法生成时间路径组件")
                        return False
                    
                    # 验证路径组件格式
                    if len(path_component) != 14:  # YYYYMMDDHHMMSS
                        self.log_test("文件路径生成", False, f"时间路径组件格式不正确: {path_component}")
                        return False
                    
                    self.log_test("文件路径生成", True)
                    return True
                    
                except AttributeError as e:
                    self.log_test("文件路径生成", False, f"bao_start不是datetime对象: {e}")
                    return False
                
        except Exception as e:
            self.log_test("文件路径生成", False, str(e))
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始字段类型迁移测试...")
        print("=" * 50)
        
        tests = [
            self.test_database_connection,
            self.test_table_structure,
            self.test_model_fields,
            self.test_data_serialization,
            self.test_datetime_handling,
            self.test_file_path_generation
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        print("\n" + "=" * 50)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！字段类型迁移成功！")
            return True
        else:
            print("⚠️  部分测试失败，请检查问题后重新测试")
            return False
    
    def generate_report(self):
        """生成测试报告"""
        report = {
            'timestamp': datetime.datetime.now().isoformat(),
            'total_tests': len(self.test_results),
            'passed_tests': sum(1 for r in self.test_results if r['success']),
            'failed_tests': sum(1 for r in self.test_results if not r['success']),
            'results': self.test_results
        }
        
        with open('migration_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 测试报告已保存到: migration_test_report.json")

if __name__ == "__main__":
    tester = FieldMigrationTester()
    success = tester.run_all_tests()
    tester.generate_report()
    
    sys.exit(0 if success else 1)
